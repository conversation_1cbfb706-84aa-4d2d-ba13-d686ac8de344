/* Enhanced Material Design 3 Golf App Styles - Combined */

/* CSS Variables for Material Design 3 Colors */
:root {
    /* Primary Colors */
    --md-primary: #4CAF50;
    --md-primary-dark: #388E3C;
    --md-primary-light: #81C784;

    /* Surface Colors */
    --md-surface: #FFFFFF;
    --md-surface-variant: #F5F5F5;
    --md-surface-container: #FAFAFA;

    /* Text Colors */
    --md-on-surface: #1C1B1F;
    --md-on-surface-variant: #49454F;
    --md-on-primary: #FFFFFF;

    /* Error Colors */
    --md-error: #BA1A1A;

    /* Elevation Shadows */
    --elevation-1: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24);
    --elevation-2: 0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23);
    --elevation-3: 0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23);

    /* Typography */
    --font-family: 'Roboto', sans-serif;
}

/* Reset and Base Styles with Touch-Friendly Enhancements */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: rgba(76, 175, 80, 0.2);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

input, textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

body {
    font-family: var(--font-family);
    background-color: #000;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    position: relative;
}

/* Enhanced App Header with Logo */
.app-header {
    position: fixed;
    top: 24px; /* Below status bar */
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 8px 16px;
    pointer-events: none; /* Allow touches to pass through to content below */
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

/* Show header logo on all screens except welcome screen */
body:not(.welcome-active) .app-header {
    opacity: 1;
    visibility: visible;
}

.header-logo {
    display: flex;
    justify-content: flex-start;
    pointer-events: auto; /* Enable touches on logo */
}

.header-logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.header-logo-img:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
}

.header-logo-img:active {
    transform: scale(0.95);
}

/* Club Logo Styles (for welcome screen) */
.club-logo {
    width: 120px;
    height: 120px;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.club-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
}

/* Android Device Frame */
.status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 24px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
}

.status-left .time {
    font-weight: 600;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-right .material-icons {
    font-size: 14px;
}

.battery {
    font-size: 11px;
    margin-left: 2px;
}

.navigation-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 32px;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.nav-indicator {
    width: 134px;
    height: 4px;
    background: white;
    border-radius: 2px;
    opacity: 0.8;
}

/* App Container with Header Logo Space */
.app-container {
    position: relative;
    width: 100%;
    height: calc(100vh - 56px);
    margin-top: 24px;
    padding-top: 56px; /* Space for header logo */
    overflow: hidden;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    z-index: 1;
}

.screen.active {
    opacity: 1;
    transform: translateX(0);
    z-index: 2;
}

.screen-content {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

/* Background Images */
.background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: 1;
}

.golf-course-bg {
    background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-course-bg.blurred {
    filter: blur(2px);
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-course-bg.dark-overlay {
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-course-bg.gradient-overlay {
    background-image: linear-gradient(135deg, rgba(76, 175, 80, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%),
                      url('https://images.unsplash.com/photo-1535131749006-b7f58c99034b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

.golf-pattern-bg {
    background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95)),
                      url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%234CAF50" opacity="0.1"/></svg>');
    background-size: 50px 50px;
}

.golf-sunset-bg {
    background-image: linear-gradient(rgba(255, 193, 7, 0.3), rgba(255, 87, 34, 0.3)),
                      url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
}

/* Welcome Screen */
.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.app-logo {
    position: relative;
    margin-bottom: 24px;
}

.app-logo .fa-golf-ball {
    font-size: 48px;
    color: white;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.app-logo .flag-icon {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 24px;
    color: var(--md-primary);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.app-title {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin-bottom: 32px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.welcome-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 48px;
    box-shadow: var(--elevation-2);
}

.tagline {
    font-size: 16px;
    color: var(--md-on-surface-variant);
    font-weight: 400;
}

/* Material Design Components */

/* Floating Action Button */
.fab {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: var(--md-primary);
    color: var(--md-on-primary);
    border: none;
    border-radius: 28px;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 500;
    font-family: var(--font-family);
    box-shadow: var(--elevation-3);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.fab:hover {
    box-shadow: var(--elevation-3), 0 0 0 8px rgba(76, 175, 80, 0.12);
    transform: translateY(-1px);
}

.fab:active {
    transform: translateY(0);
    box-shadow: var(--elevation-2);
}

.fab.primary {
    background: var(--md-primary);
}

.payment-fab {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 200px;
}

/* Enhanced Floating Action Button with Touch Improvements */
.enhanced-fab {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 28px;
    padding: 22px 36px; /* Increased padding for better touch target */
    font-size: 16px;
    font-weight: 600;
    font-family: var(--font-family);
    box-shadow:
        0 8px 16px rgba(76, 175, 80, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.2) inset;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 180px; /* Increased minimum width */
    min-height: 64px; /* Minimum touch target height */
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.enhanced-fab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.enhanced-fab:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 24px rgba(76, 175, 80, 0.4),
        0 1px 0 rgba(255, 255, 255, 0.3) inset;
}

.enhanced-fab:hover::before {
    left: 100%;
}

.enhanced-fab:active {
    transform: translateY(0);
    box-shadow:
        0 4px 8px rgba(76, 175, 80, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.2) inset;
}

/* Cards */
.content-card, .details-card, .payment-card {
    background: var(--md-surface);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--elevation-1);
    margin-bottom: 16px;
}

/* Enhanced Card Variants with Touch Improvements */
.enhanced-card {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
    /* Touch improvements */
    cursor: pointer;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.5), transparent);
}

.enhanced-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

/* Premium Tee Time Cards with Touch Enhancements */
.premium-tee-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-radius: 20px;
    padding: 32px 24px; /* Increased vertical padding */
    margin: 0 auto 20px auto; /* Center the cards and add bottom margin */
    max-width: 380px; /* Limit the maximum width */
    width: 100%; /* Allow responsive scaling */
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    backdrop-filter: blur(20px);
    /* Touch improvements */
    cursor: pointer;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    touch-action: manipulation;
    min-height: 160px; /* Ensure minimum touch target size */
}

.premium-tee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--md-primary), var(--md-primary-light));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.premium-tee-card:hover::before {
    opacity: 1;
}

.premium-tee-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 24px rgba(76, 175, 80, 0.15),
        0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--md-primary);
}

.tee-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.tee-time-display {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: 8px;
}

.tee-time-main {
    font-size: 24px;
    font-weight: 700;
    color: var(--md-on-surface);
    line-height: 1;
}

.tee-time-period {
    font-size: 14px;
    font-weight: 500;
    color: var(--md-on-surface-variant);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.availability-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.availability-badge.available {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.availability-badge.limited {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.availability-badge.full {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.availability-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Tee Card Details and Player Selection */
.tee-card-details {
    margin-bottom: 24px;
}

.tee-info-group {
    margin-bottom: 20px;
}

.player-selection {
    margin-bottom: 20px;
}

.player-selection-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin-bottom: 12px;
}

.player-selection-label .material-icons {
    font-size: 18px;
    color: var(--md-primary);
}

.player-count-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.player-count-btn {
    width: 48px;
    height: 48px;
    border: 2px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    color: var(--md-on-surface);
    font-size: 16px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.player-count-btn:hover {
    border-color: var(--md-primary);
    background: rgba(76, 175, 80, 0.1);
    transform: scale(1.05);
}

.player-count-btn.selected {
    border-color: var(--md-primary);
    background: var(--md-primary);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.price-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
}

.price-main {
    font-size: 32px;
    font-weight: 700;
    color: var(--md-primary);
    line-height: 1;
    margin-bottom: 4px;
}

.price-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--md-on-surface-variant);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* App Bar */
.app-bar {
    display: flex;
    align-items: center;
    padding: 16px 0;
    margin-bottom: 16px;
}

.app-bar-title {
    font-size: 22px;
    font-weight: 500;
    color: white;
    margin-left: 16px;
}

.icon-button {
    width: 48px; /* Increased from default */
    height: 48px; /* Increased from default */
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.icon-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.icon-button .material-icons {
    color: white;
    font-size: 24px;
}

/* Text Input Layout (Material Design) */
.text-input-layout {
    position: relative;
    margin-bottom: 24px;
}

.text-input-layout input,
.text-input-layout textarea {
    width: 100%;
    padding: 16px 12px 8px 12px;
    border: none;
    outline: none;
    background: transparent;
    font-size: 16px;
    font-family: var(--font-family);
    color: var(--md-on-surface);
}

.text-input-layout textarea {
    min-height: 80px;
    resize: vertical;
}

.text-input-layout label {
    position: absolute;
    left: 12px;
    top: 16px;
    font-size: 16px;
    color: var(--md-on-surface-variant);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    pointer-events: none;
}

.text-input-layout input:focus + label,
.text-input-layout input:not(:placeholder-shown) + label,
.text-input-layout textarea:focus + label,
.text-input-layout textarea:not(:placeholder-shown) + label {
    top: 4px;
    font-size: 12px;
    color: var(--md-primary);
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--md-on-surface-variant);
    opacity: 0.38;
}

.input-line::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--md-primary);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    transform: translateX(-50%);
}

.text-input-layout input:focus ~ .input-line::after,
.text-input-layout textarea:focus ~ .input-line::after {
    width: 100%;
}

/* Enhanced Input Fields with Touch Improvements */
.enhanced-input-group {
    position: relative;
    margin-bottom: 28px; /* Increased margin for better spacing */
}

.enhanced-input {
    width: 100%;
    padding: 20px 24px; /* Increased padding for better touch target */
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    font-size: 16px; /* Minimum 16px to prevent zoom on iOS */
    font-family: var(--font-family);
    color: var(--md-on-surface);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    min-height: 56px; /* Minimum touch target height */
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.enhanced-input:focus {
    outline: none;
    border-color: var(--md-primary);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}

.enhanced-input-label {
    position: absolute;
    left: 24px; /* Adjusted for new padding */
    top: 20px; /* Adjusted for new padding */
    font-size: 16px;
    color: var(--md-on-surface-variant);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    pointer-events: none;
    background: linear-gradient(to bottom, transparent 40%, rgba(255, 255, 255, 0.8) 40%);
    padding: 0 4px;
    /* Touch improvements */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.enhanced-input:focus + .enhanced-input-label,
.enhanced-input:not(:placeholder-shown) + .enhanced-input-label {
    top: -8px;
    font-size: 12px;
    color: var(--md-primary);
    font-weight: 600;
}

/* Phone Input */
.phone-input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.country-picker {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border-radius: 8px;
    background: var(--md-surface-variant);
    cursor: pointer;
    min-width: 80px;
}

.country-picker .flag {
    width: 20px;
    height: auto;
}

.country-picker span {
    font-weight: 500;
}

.phone-input-container .text-input-layout {
    flex: 1;
}

.helper-text {
    font-size: 12px;
    color: var(--md-on-surface-variant);
    margin-top: 4px;
}

/* Enhanced Phone Screen Styles */
.phone-screen-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 120px);
}

.phone-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
    padding: 40px 32px;
    max-width: 420px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.phone-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--md-primary) 0%, var(--md-primary-light) 50%, var(--md-primary) 100%);
}

.phone-header {
    text-align: center;
    margin-bottom: 40px;
}

.phone-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--md-on-surface);
    margin-bottom: 12px;
}

.phone-subtitle {
    font-size: 16px;
    color: var(--md-on-surface-variant);
    line-height: 1.5;
}

.phone-input-section {
    margin-bottom: 40px;
}

.enhanced-phone-input {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.country-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    min-height: 56px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.country-selector:hover {
    border-color: var(--md-primary);
    background: rgba(255, 255, 255, 0.95);
}

.country-flag {
    width: 20px;
    height: auto;
    border-radius: 2px;
}

.country-code {
    font-size: 16px;
    font-weight: 600;
    color: var(--md-on-surface);
}

.country-selector .material-icons {
    font-size: 20px;
    color: var(--md-on-surface-variant);
}

.phone-input-wrapper {
    flex: 1;
}

.phone-number-input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    font-size: 16px;
    font-family: var(--font-family);
    color: var(--md-on-surface);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    outline: none;
    /* Touch improvements */
    min-height: 56px;
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.phone-number-input:focus {
    border-color: var(--md-primary);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}

.phone-number-input::placeholder {
    color: var(--md-on-surface-variant);
    opacity: 0.7;
}

.input-helper-text {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--md-on-surface-variant);
    padding: 0 4px;
}

.input-helper-text .material-icons {
    font-size: 16px;
    opacity: 0.7;
}

.phone-actions {
    display: flex;
    justify-content: center;
}

.phone-submit-btn {
    min-width: 200px;
}

/* Screen Titles */
.screen-title {
    font-size: 24px;
    font-weight: 500;
    color: var(--md-on-surface);
    margin-bottom: 24px;
}

/* Date Picker */
.date-picker {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    overflow-x: auto;
    padding: 8px 0;
}

.date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 16px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 60px;
}

.date-item.selected {
    background: var(--md-primary);
    transform: scale(1.05);
}

.date-day {
    font-size: 12px;
    font-weight: 400;
    opacity: 0.8;
}

.date-number {
    font-size: 18px;
    font-weight: 500;
    margin-top: 4px;
}

/* Tee Time Cards */
.tee-times-list {
    flex: 1;
    overflow-y: auto;
}

.tee-time-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--elevation-1);
}

.time-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.time {
    font-size: 18px;
    font-weight: 500;
    color: var(--md-on-surface);
}

.players {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: var(--md-on-surface-variant);
}

.players .material-icons {
    font-size: 16px;
}

.price {
    font-size: 20px;
    font-weight: 600;
    color: var(--md-primary);
}

.select-button {
    background: var(--md-primary);
    color: var(--md-on-primary);
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.select-button:hover {
    background: var(--md-primary-dark);
    transform: translateY(-1px);
}

/* Enhanced Select Button with Touch Improvements */
.enhanced-select-button {
    width: 100%;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 20px 24px; /* Increased padding for better touch target */
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
    /* Touch improvements */
    min-height: 60px; /* Minimum 60px touch target */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.enhanced-select-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.enhanced-select-button:hover::before {
    left: 100%;
}

.enhanced-select-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
}

.enhanced-select-button:active {
    transform: translateY(0);
}

.enhanced-select-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
    pointer-events: none;
    transform: none;
    box-shadow: none;
}

.enhanced-select-button:disabled::before {
    display: none;
}

/* Tee Time Screen Layout */
#tee-time-screen .screen-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tee-times-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 4px;
    /* Touch improvements */
    -webkit-overflow-scrolling: touch;
}

.tee-times-list::-webkit-scrollbar {
    width: 4px;
}

.tee-times-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.tee-times-list::-webkit-scrollbar-thumb {
    background: rgba(76, 175, 80, 0.3);
    border-radius: 2px;
}

.tee-times-list::-webkit-scrollbar-thumb:hover {
    background: rgba(76, 175, 80, 0.5);
}

/* Current Date Display */
.current-date-display {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 32px;
    padding: 20px 16px;
}

.current-date-text {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 16px 32px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    /* Touch improvements */
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.current-date-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0.5;
}

/* Enhanced Help Banner with Touch Improvements */
.help-banner {
    padding: 20px 24px; /* Increased padding */
    margin: 24px 0; /* Increased margin */
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    color: white;
    /* Touch improvements */
    min-height: 60px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.help-banner:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

/* Enhanced Verification Screen Styles */
.verification-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 200px);
    padding: 20px;
}

.verification-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.5) inset;
    text-align: center;
    padding: 40px 32px;
    max-width: 420px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.verification-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--md-primary) 0%, var(--md-primary-light) 50%, var(--md-primary) 100%);
}

.verification-header {
    margin-bottom: 40px;
}

.verification-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
}

.verification-icon .material-icons {
    font-size: 36px;
    color: white;
}

.verification-subtitle {
    font-size: 16px;
    color: var(--md-on-surface-variant);
    line-height: 1.5;
    margin-top: 12px;
}

.verification-subtitle strong {
    color: var(--md-primary);
    font-weight: 600;
}

/* Code Input Styling with Touch Improvements */
.verification-code-input {
    display: flex;
    gap: 12px; /* Reduced gap to fit better in narrower card */
    justify-content: center;
    margin-bottom: 32px;
    padding: 0 16px;
    /* Touch improvements */
    -webkit-overflow-scrolling: touch;
}

.code-digit {
    width: 48px; /* Reduced width to fit better in narrower card */
    height: 56px; /* Reduced height proportionally */
    border: 2px solid rgba(76, 175, 80, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    text-align: center;
    font-size: 22px; /* Slightly smaller font */
    font-weight: 700;
    font-family: var(--font-family);
    color: var(--md-on-surface);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    outline: none;
    /* Touch improvements */
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    flex-shrink: 0; /* Prevent shrinking on mobile */
}

.code-digit:focus {
    border-color: var(--md-primary);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
    transform: scale(1.05);
}

.code-digit:not(:placeholder-shown) {
    border-color: var(--md-primary);
    background: rgba(76, 175, 80, 0.05);
}

.code-digit.error {
    border-color: var(--md-error);
    background: rgba(186, 26, 26, 0.05);
    animation: shakeError 0.5s ease-in-out;
}

@keyframes shakeError {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-4px); }
    75% { transform: translateX(4px); }
}

/* Verification Timer */
.verification-timer {
    margin-bottom: 32px;
}

.timer-text {
    font-size: 14px;
    color: var(--md-on-surface-variant);
    margin-bottom: 16px;
}

.timer-text span {
    color: var(--md-primary);
    font-weight: 600;
}

.resend-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    border: 2px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    padding: 16px 24px; /* Increased padding for better touch target */
    color: var(--md-primary);
    font-size: 14px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    margin: 0 auto;
    /* Touch improvements */
    min-height: 52px; /* Minimum touch target height */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.resend-button:disabled {
    border-color: rgba(0, 0, 0, 0.12);
    color: rgba(0, 0, 0, 0.38);
    cursor: not-allowed;
}

.resend-button:not(:disabled):hover {
    background: rgba(76, 175, 80, 0.08);
    border-color: var(--md-primary);
    transform: translateY(-1px);
}

.resend-button .material-icons {
    font-size: 18px;
}

/* Verification FAB */
.verification-fab {
    opacity: 0.5;
    cursor: not-allowed;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: block;
    margin: 32px auto 0 auto;
    position: relative;
}

.verification-fab:not(:disabled) {
    opacity: 1;
    cursor: pointer;
}

.verification-fab:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(76, 175, 80, 0.4);
}

/* Success Animation for Valid Code */
.code-success {
    border-color: #4CAF50 !important;
    background: rgba(76, 175, 80, 0.1) !important;
    animation: pulseSuccess 0.3s ease-in-out;
}

@keyframes pulseSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Detail Items */
.detail-item {
    margin-bottom: 20px;
}

.detail-item label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: var(--md-on-surface-variant);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    color: var(--md-on-surface);
}

.edit-icon {
    color: var(--md-on-surface-variant);
    cursor: pointer;
    transition: color 0.2s;
}

.edit-icon:hover {
    color: var(--md-primary);
}

/* Enhanced Action Buttons with Touch Improvements */
.action-buttons {
    display: flex;
    gap: 16px;
    margin-top: 32px;
    padding: 0 4px; /* Small padding for better visual spacing */
}

.button {
    flex: 1;
    padding: 18px 24px; /* Increased padding for touch */
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    /* Touch improvements */
    min-height: 56px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.button.outlined {
    background: transparent;
    border: 2px solid rgba(76, 175, 80, 0.3);
    color: var(--md-primary);
}

.button.outlined:hover {
    background: rgba(76, 175, 80, 0.08);
    border-color: var(--md-primary);
    transform: translateY(-1px);
}

.button.contained {
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.button.contained:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.4);
}

/* Payment Screen */
.payment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.payment-header h3 {
    font-size: 20px;
    font-weight: 500;
    color: var(--md-on-surface);
}

.security-icon {
    color: var(--md-primary);
    font-size: 24px;
}

.card-preview {
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-dark) 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.card-preview::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
}

.card-number {
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 2px;
    margin-bottom: 16px;
}

.card-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.card-brand {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 24px;
}

.input-row {
    display: flex;
    gap: 16px;
}

.input-row .text-input-layout {
    flex: 1;
}

.payment-footer {
    position: fixed;
    bottom: 140px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.payment-logos {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 8px;
}

.payment-logos i {
    font-size: 32px;
    color: var(--md-on-surface-variant);
}

.secure-text {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--md-on-surface-variant);
}

/* Success Screen */
.success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.success-animation {
    margin-bottom: 32px;
}

.checkmark-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--md-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: checkmarkPulse 0.6s ease-out;
}

.checkmark {
    color: white;
    font-size: 40px;
    animation: checkmarkScale 0.3s ease-out 0.3s both;
}

@keyframes checkmarkPulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes checkmarkScale {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

.success-title {
    font-size: 28px;
    font-weight: 600;
    color: white;
    margin-bottom: 32px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.booking-summary {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 48px;
    box-shadow: var(--elevation-2);
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 16px;
    color: var(--md-on-surface);
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-item .material-icons {
    color: var(--md-primary);
    font-size: 20px;
}

/* Enhanced Android-Style Booking Details Screen */
.modern-booking-header {
    margin-bottom: 32px;
}

.booking-info-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: none;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 28px;
    position: relative;
    overflow: hidden;
    max-width: 420px;
    margin: 0 auto;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.booking-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 50%, #4CAF50 100%);
    border-radius: 24px 24px 0 0;
}

.booking-info-row {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.booking-info-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 4px 0;
}

.booking-icon {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0.08) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.booking-icon .material-icons {
    color: #4CAF50;
    font-size: 26px;
    font-weight: 500;
}

.booking-text {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.booking-label {
    font-size: 13px;
    font-weight: 700;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}

.booking-value {
    font-size: 17px;
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.4;
    margin: 0;
}

/* Enhanced Players Section */
.modern-players-section {
    margin-bottom: 32px;
}

.section-header {
    margin-bottom: 20px;
    padding: 0 8px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 19px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0;
    letter-spacing: 0.2px;
}

.section-title .material-icons {
    color: #4CAF50;
    font-size: 22px;
    font-weight: 500;
}

.modern-player-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 420px;
    margin: 0 auto;
}

/* Enhanced Player Card */
.modern-player-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: none;
    box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.modern-player-card:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08);
}

.player-card-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 50%, #4CAF50 100%);
    border-radius: 20px 20px 0 0;
}

.player-card-content {
    padding: 24px;
}

.player-main-info {
    display: flex;
    align-items: center;
    gap: 18px;
    margin-bottom: 18px;
}

.player-avatar-modern {
    width: 54px;
    height: 54px;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0.08) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.player-avatar-modern .material-icons {
    color: #4CAF50;
    font-size: 28px;
    font-weight: 500;
}

.player-details-modern {
    flex: 1;
}

.player-name {
    font-size: 17px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 6px 0;
    letter-spacing: 0.2px;
}

.player-role {
    font-size: 13px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin: 0;
}

.player-price-modern {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.price-amount {
    font-size: 20px;
    font-weight: 800;
    color: #4CAF50;
    line-height: 1;
    letter-spacing: 0.2px;
}

.price-label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-top: 4px;
}

.player-features {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.feature-tag {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.12) 0%, rgba(76, 175, 80, 0.06) 100%);
    border: 1px solid rgba(76, 175, 80, 0.25);
    border-radius: 24px;
    font-size: 12px;
    font-weight: 600;
    color: #4CAF50;
    letter-spacing: 0.3px;
    box-shadow: 0 1px 4px rgba(76, 175, 80, 0.15);
}

.feature-tag .material-icons {
    font-size: 15px;
    font-weight: 500;
}

/* Enhanced Summary Card */
.modern-summary-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: none;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 28px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
    max-width: 420px;
    margin: 0 auto 32px auto;
}

.modern-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 50%, #4CAF50 100%);
    border-radius: 24px 24px 0 0;
}

.summary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.summary-header h3 {
    font-size: 19px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0;
    letter-spacing: 0.2px;
}

.summary-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(34, 197, 94, 0.08) 100%);
    border: 1px solid rgba(34, 197, 94, 0.25);
    border-radius: 24px;
    font-size: 12px;
    font-weight: 700;
    color: #059669;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 1px 4px rgba(34, 197, 94, 0.2);
}

.summary-badge .material-icons {
    font-size: 15px;
    font-weight: 500;
}

.summary-breakdown {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;
    color: #666;
    padding: 2px 0;
}

.summary-item span:first-child {
    font-weight: 500;
    letter-spacing: 0.2px;
}

.summary-item span:last-child {
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: 0.2px;
}

.summary-divider {
    height: 2px;
    background: linear-gradient(90deg, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.05) 50%, rgba(76, 175, 80, 0.2) 100%);
    border-radius: 1px;
    margin: 8px 0;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 17px;
    font-weight: 700;
    color: #1a1a1a;
    padding-top: 12px;
    letter-spacing: 0.3px;
}

.summary-total span:last-child {
    font-size: 22px;
    font-weight: 800;
    color: #4CAF50;
    letter-spacing: 0.2px;
}

/* Enhanced Proceed Button */
.modern-proceed-button {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
    display: block;
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    border: none;
    border-radius: 20px;
    padding: 22px 28px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 6px 20px rgba(76, 175, 80, 0.3),
        0 2px 8px rgba(76, 175, 80, 0.2);
    /* Touch improvements */
    min-height: 68px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.modern-proceed-button:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 32px rgba(76, 175, 80, 0.4),
        0 4px 12px rgba(76, 175, 80, 0.25);
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: white;
    font-size: 17px;
    font-weight: 700;
    font-family: var(--font-family);
    position: relative;
    z-index: 2;
    letter-spacing: 0.3px;
}

.button-content .material-icons {
    font-size: 22px;
    font-weight: 500;
}

.button-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    transition: left 0.6s ease;
}

.modern-proceed-button:hover .button-shine {
    left: 100%;
}

/* Enhanced Success Animation */
.enhanced-success-animation {
    margin-bottom: 40px;
    position: relative;
}

.enhanced-checkmark-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--md-primary) 0%, var(--md-primary-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: enhancedCheckmarkPulse 0.8s ease-out;
    box-shadow:
        0 0 0 0 rgba(76, 175, 80, 0.4),
        0 8px 16px rgba(76, 175, 80, 0.3);
    position: relative;
}

.enhanced-checkmark-circle::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid var(--md-primary);
    opacity: 0.3;
    animation: enhancedRipple 2s infinite;
}

.enhanced-checkmark {
    color: white;
    font-size: 48px;
    animation: enhancedCheckmarkScale 0.4s ease-out 0.4s both;
}

@keyframes enhancedCheckmarkPulse {
    0% {
        transform: scale(0);
        opacity: 0;
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(76, 175, 80, 0);
    }
    100% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

@keyframes enhancedCheckmarkScale {
    0% {
        transform: scale(0) rotate(-45deg);
    }
    50% {
        transform: scale(1.2) rotate(-45deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
    }
}

@keyframes enhancedRipple {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Enhanced Snackbar with Touch Improvements */
.snackbar {
    position: fixed;
    bottom: 100px;
    left: 16px;
    right: 16px;
    padding: 16px 24px; /* Increased padding */
    border-radius: 12px;
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    /* Touch improvements */
    min-height: 56px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.snackbar.show {
    transform: translateY(0);
    opacity: 1;
}

.enhanced-snackbar {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.1) inset;
    backdrop-filter: blur(20px);
}

.snackbar-action {
    background: transparent;
    border: none;
    color: var(--md-primary);
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    /* Touch improvements */
    min-height: 40px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.snackbar-action:hover {
    background: rgba(76, 175, 80, 0.1);
}

/* Enhanced Responsive Design with Touch Improvements */
@media (max-width: 480px) {
    .app-header {
        padding: 6px 12px;
    }

    .header-logo-img {
        width: 36px;
        height: 36px;
    }

    .club-logo {
        width: 100px;
        height: 100px;
    }

    .screen-content {
        padding: 12px;
    }

    .app-title {
        font-size: 28px;
    }

    .welcome-card {
        padding: 20px;
    }

    .content-card, .details-card, .payment-card {
        padding: 20px;
    }

    .premium-tee-card {
        padding: 28px 20px; /* Increased vertical padding for touch */
        border-radius: 16px;
        margin: 0 auto 24px auto; /* Center and increase spacing */
        max-width: 340px; /* Slightly smaller on mobile */
    }

    .tee-time-main {
        font-size: 20px;
    }

    .price-main {
        font-size: 28px;
    }

    /* Touch-specific improvements for mobile */
    .enhanced-select-button {
        padding: 24px; /* Larger touch target on mobile */
        min-height: 68px;
        font-size: 16px;
    }

    .enhanced-fab {
        padding: 24px 40px; /* Larger touch target */
        min-height: 68px;
        min-width: 200px;
    }

    .current-date-text {
        padding: 14px 24px; /* Adjusted for mobile */
        font-size: 16px;
        min-height: 52px;
    }

    .action-buttons {
        flex-direction: column; /* Stack buttons on mobile */
        gap: 12px;
    }

    .button {
        padding: 20px 24px; /* Larger touch targets on mobile */
        min-height: 60px;
        width: 100%;
    }

    .icon-button {
        width: 44px; /* Slightly smaller on mobile but still touch-friendly */
        height: 44px;
    }

    /* Verification screen mobile adjustments */
    .verification-container {
        padding: 16px;
        min-height: calc(100vh - 160px);
    }

    .verification-card {
        padding: 32px 24px;
        border-radius: 20px;
    }

    .verification-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
    }

    .verification-icon .material-icons {
        font-size: 32px;
    }

    .verification-code-input {
        gap: 8px;
        padding: 0 8px;
    }

    .code-digit {
        width: 42px;
        height: 52px;
        font-size: 20px;
    }

    .verification-fab {
        margin: 24px auto 0 auto;
    }

    /* Phone screen mobile adjustments */
    .phone-screen-content {
        padding: 16px;
        min-height: calc(100vh - 160px);
    }

    .phone-container {
        padding: 32px 24px;
        border-radius: 20px;
    }

    .phone-header {
        margin-bottom: 32px;
    }

    .phone-title {
        font-size: 22px;
    }

    .enhanced-phone-input {
        flex-direction: column;
        gap: 16px;
    }

    .country-selector {
        justify-content: center;
        padding: 18px 16px;
    }

    .phone-number-input {
        padding: 18px 20px;
    }

    .phone-submit-btn {
        min-width: 180px;
        padding: 20px 32px;
    }

    /* Tee time screen mobile adjustments */
    #tee-time-screen .screen-content {
        padding: 12px;
    }

    .tee-times-list {
        padding: 0 2px;
    }

    .player-count-btn {
        width: 44px;
        height: 44px;
        font-size: 14px;
    }

    .price-main {
        font-size: 28px;
    }

    .player-selection-label {
        font-size: 13px;
    }

    .player-selection-label .material-icons {
        font-size: 16px;
    }

    /* Enhanced mobile booking details adjustments */
    .booking-info-card,
    .modern-player-cards,
    .modern-summary-card,
    .modern-proceed-button {
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
    }

    .booking-info-card,
    .modern-player-card,
    .modern-summary-card {
        padding: 24px 20px;
        border-radius: 20px;
    }

    .booking-info-row {
        gap: 20px;
    }

    .booking-info-item {
        gap: 16px;
    }

    .booking-icon {
        width: 48px;
        height: 48px;
    }

    .booking-icon .material-icons {
        font-size: 24px;
    }

    .booking-value {
        font-size: 16px;
    }

    .player-main-info {
        gap: 16px;
        margin-bottom: 16px;
    }

    .player-avatar-modern {
        width: 48px;
        height: 48px;
    }

    .player-avatar-modern .material-icons {
        font-size: 24px;
    }

    .player-name {
        font-size: 16px;
    }

    .price-amount {
        font-size: 18px;
    }

    .feature-tag {
        padding: 6px 12px;
        font-size: 11px;
    }

    .modern-proceed-button {
        padding: 20px 24px;
        min-height: 64px;
        border-radius: 18px;
    }

    .button-content {
        font-size: 16px;
    }

    .summary-header h3 {
        font-size: 18px;
    }

    .summary-total span:last-child {
        font-size: 20px;
    }
}

/* Ripple Effect */
.fab::before,
.button::before,
.select-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.fab:active::before,
.button:active::before,
.select-button:active::before {
    width: 200px;
    height: 200px;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Focus States for Accessibility */
.fab:focus,
.button:focus,
.icon-button:focus,
.select-button:focus {
    outline: 2px solid var(--md-primary);
    outline-offset: 2px;
}

.text-input-layout input:focus,
.text-input-layout textarea:focus {
    outline: none;
}

@media (max-width: 320px) {
    .header-logo-img {
        width: 32px;
        height: 32px;
    }

    .club-logo {
        width: 80px;
        height: 80px;
    }
}

/* Enhanced Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .enhanced-card,
    .premium-tee-card {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
        border-color: rgba(76, 175, 80, 0.3);
    }

    .enhanced-input {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
        color: #e2e8f0;
    }

    .enhanced-input:focus {
        background: rgba(30, 41, 59, 0.95);
    }

    .current-date-text {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(76, 175, 80, 0.3);
        color: #e2e8f0;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --md-primary: #2E7D32;
        --md-on-surface: #000000;
        --md-on-surface-variant: #424242;
    }

    .background-image {
        filter: contrast(1.2);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}