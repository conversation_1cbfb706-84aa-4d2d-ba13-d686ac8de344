create clear and llm undertandable prompt:
design app prototype using html, css, use icons and animation as much as possible, large kiosk screent, orientation as portrait, use golf course image as background as much as possible, add as much design elements or words as possible, make it more realistic android looking and feel
check in app for golfers at the golf course: screen 1: welcome screen with logo and golf course background screen 2: phone number validation screen 3: check in tee time screen 3: tee time details with 
editing screen 5: payment screen 6: success