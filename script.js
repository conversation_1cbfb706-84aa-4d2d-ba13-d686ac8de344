// Golf Check-in App JavaScript

// Global variables
let currentScreen = 'welcome-screen';
let selectedPlayerCount = 0;
let verificationTimer = 30;
let timerInterval;

// Screen management
function showScreen(screenId) {
    // Hide all screens
    const screens = document.querySelectorAll('.screen');
    screens.forEach(screen => {
        screen.classList.remove('active');
    });

    // Show the target screen
    const targetScreen = document.getElementById(screenId);
    if (targetScreen) {
        targetScreen.classList.add('active');
        currentScreen = screenId;

        // Update body class for welcome screen
        if (screenId === 'welcome-screen') {
            document.body.classList.add('welcome-active');
        } else {
            document.body.classList.remove('welcome-active');
        }

        // Initialize screen-specific functionality
        initializeScreen(screenId);
    }
}

// Initialize screen-specific functionality
function initializeScreen(screenId) {
    switch (screenId) {
        case 'verification-screen':
            startVerificationTimer();
            break;
        case 'tee-time-screen':
            updateCurrentDate();
            break;
        case 'success-screen':
            showSnackbar();
            break;
    }
}

// Phone number validation and submission
function submitPhoneNumber() {
    const phoneInput = document.getElementById('phone');
    const phoneNumber = phoneInput.value.trim();

    if (phoneNumber.length >= 10) {
        // Simulate phone number submission
        const submitBtn = document.querySelector('.phone-submit-btn');
        submitBtn.classList.add('loading');

        setTimeout(() => {
            submitBtn.classList.remove('loading');
            showScreen('verification-screen');
        }, 2000);
    } else {
        alert('Please enter a valid phone number');
    }
}

// Verification code handling
function moveToNext(current, nextId) {
    if (current.value.length === 1 && nextId) {
        document.getElementById(nextId).focus();
    }
    checkVerificationCode();
}

function moveToPrev(current, prevId, event) {
    if (event.key === 'Backspace' && current.value === '' && prevId) {
        document.getElementById(prevId).focus();
    }
}

function checkVerificationCode() {
    const inputs = document.querySelectorAll('.code-digit');
    let code = '';

    inputs.forEach(input => {
        code += input.value;
    });

    const verifyBtn = document.getElementById('verify-btn');
    if (code.length === 6) {
        verifyBtn.disabled = false;
        verifyBtn.classList.remove('disabled');
    } else {
        verifyBtn.disabled = true;
        verifyBtn.classList.add('disabled');
    }
}

function verifyCode() {
    const inputs = document.querySelectorAll('.code-digit');
    let code = '';

    inputs.forEach(input => {
        code += input.value;
    });

    if (code.length === 6) {
        // Simulate verification
        inputs.forEach(input => {
            input.classList.add('code-success');
        });

        setTimeout(() => {
            showScreen('tee-time-screen');
        }, 1000);
    }
}

function startVerificationTimer() {
    verificationTimer = 30;
    const countdown = document.getElementById('countdown');
    const resendBtn = document.getElementById('resend-btn');

    resendBtn.disabled = true;

    timerInterval = setInterval(() => {
        verificationTimer--;
        countdown.textContent = verificationTimer;

        if (verificationTimer <= 0) {
            clearInterval(timerInterval);
            resendBtn.disabled = false;
            countdown.parentElement.style.display = 'none';
        }
    }, 1000);
}

function resendCode() {
    // Clear all input fields
    const inputs = document.querySelectorAll('.code-digit');
    inputs.forEach(input => {
        input.value = '';
        input.classList.remove('code-success', 'error');
    });

    // Restart timer
    document.querySelector('.timer-text').style.display = 'block';
    startVerificationTimer();

    // Focus first input
    document.getElementById('code-1').focus();
}

// Player count selection
function selectPlayerCount(button, count) {
    // Remove selected class from all buttons in the same card
    const card = button.closest('.premium-tee-card');
    const buttons = card.querySelectorAll('.player-count-btn');
    buttons.forEach(btn => btn.classList.remove('selected'));

    // Add selected class to clicked button
    button.classList.add('selected');

    // Enable the select button for this card
    const selectButton = card.querySelector('.enhanced-select-button');
    selectButton.disabled = false;
    selectButton.classList.remove('disabled');

    // Store the selected count
    selectedPlayerCount = count;
}

// Update current date display
function updateCurrentDate() {
    const dateElement = document.getElementById('current-date');
    const today = new Date();
    const options = {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        weekday: 'long'
    };
    const formattedDate = today.toLocaleDateString('en-US', options);
    dateElement.textContent = formattedDate;
}

// Edit dialog functions
function openEditDialog() {
    const overlay = document.getElementById('edit-dialog-overlay');
    overlay.classList.add('show');
}

function closeEditDialog() {
    const overlay = document.getElementById('edit-dialog-overlay');
    overlay.classList.remove('show');
}

function selectEditOption(button, category) {
    // Remove selected class from siblings
    const siblings = button.parentElement.querySelectorAll('.edit-option-btn');
    siblings.forEach(btn => btn.classList.remove('selected'));

    // Add selected class to clicked button
    button.classList.add('selected');
}

function saveEditChanges() {
    // Simulate saving changes
    closeEditDialog();
    showSnackbar('Changes saved successfully');
}

// Snackbar functionality
function showSnackbar(message = 'Receipt sent to email') {
    const snackbar = document.getElementById('snackbar');
    const messageElement = snackbar.querySelector('span');
    messageElement.textContent = message;

    snackbar.classList.add('show');

    setTimeout(() => {
        snackbar.classList.remove('show');
    }, 4000);
}

// Payment processing function
function startPaymentProcess() {
    const paymentFab = document.querySelector('.payment-fab');
    const progressSteps = document.querySelectorAll('.progress-step');
    const progressFill = document.querySelector('.progress-fill');

    // Disable the payment button
    paymentFab.disabled = true;
    paymentFab.style.opacity = '0.6';
    paymentFab.style.cursor = 'not-allowed';

    // Start the payment process animation
    let currentStep = 0;
    const steps = ['Card Reading', 'Processing', 'Authorization', 'Complete'];

    function animateStep(stepIndex) {
        if (stepIndex < progressSteps.length) {
            // Remove active class from previous steps
            progressSteps.forEach(step => {
                step.classList.remove('active');
                step.classList.add('completed');
            });

            // Add active class to current step
            progressSteps[stepIndex].classList.remove('completed');
            progressSteps[stepIndex].classList.add('active');

            // Update progress bar
            const progressPercentage = ((stepIndex + 1) / progressSteps.length) * 100;
            progressFill.style.width = progressPercentage + '%';

            // Move to next step after delay
            setTimeout(() => {
                if (stepIndex < progressSteps.length - 1) {
                    animateStep(stepIndex + 1);
                } else {
                    // Payment complete, go to success screen
                    setTimeout(() => {
                        showScreen('success-screen');
                    }, 1000);
                }
            }, 1500); // 1.5 seconds per step
        }
    }

    // Start the animation
    animateStep(0);
}

// Download receipt function
function downloadReceipt() {
    // Create a simple receipt content
    const receiptContent = `
RICHLAND GOLF CLUB
Booking Confirmation Receipt

Date: ${new Date().toLocaleDateString()}
Time: ${new Date().toLocaleTimeString()}

BOOKING DETAILS:
Tee Time: 8:00 AM - Tuesday, Oct 16
Course: Pebble Beach Golf Links
Players: 2 players
Total Paid: $183.60

Thank you for choosing Richland Golf Club!
    `;

    // Create a blob with the receipt content
    const blob = new Blob([receiptContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);

    // Create a temporary download link
    const a = document.createElement('a');
    a.href = url;
    a.download = 'golf-booking-receipt.txt';
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    // Show a confirmation message
    showSnackbar('Receipt downloaded successfully!');
}

// Show snackbar function
function showSnackbar(message) {
    const snackbar = document.getElementById('snackbar');
    if (snackbar) {
        snackbar.querySelector('span').textContent = message;
        snackbar.classList.add('show');

        setTimeout(() => {
            snackbar.classList.remove('show');
        }, 3000);
    }
}

// Card input formatting
function formatCardNumber(input) {
    let value = input.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    input.value = formattedValue;
}

function formatExpiry(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    input.value = value;
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Set initial screen
    showScreen('welcome-screen');

    // Add event listeners for phone input
    const phoneSubmitBtn = document.querySelector('.phone-submit-btn');
    if (phoneSubmitBtn) {
        phoneSubmitBtn.addEventListener('click', submitPhoneNumber);
    }

    // Add event listeners for card inputs
    const cardNumberInput = document.getElementById('card-number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function() {
            formatCardNumber(this);
        });
    }

    const expiryInput = document.getElementById('expiry');
    if (expiryInput) {
        expiryInput.addEventListener('input', function() {
            formatExpiry(this);
        });
    }

    // Add keyboard navigation for verification code
    const codeInputs = document.querySelectorAll('.code-digit');
    codeInputs.forEach((input, index) => {
        input.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' && index > 0) {
                codeInputs[index - 1].focus();
            } else if (e.key === 'ArrowRight' && index < codeInputs.length - 1) {
                codeInputs[index + 1].focus();
            }
        });
    });
});
