<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Golf Check-in App</title>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Roboto Font -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for additional icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Android Status Bar -->
    <div class="status-bar">
        <div class="status-left">
            <span class="time">9:41</span>
        </div>
        <div class="status-right">
            <i class="material-icons">signal_cellular_4_bar</i>
            <i class="material-icons">wifi</i>
            <span class="battery">87%</span>
            <i class="material-icons">battery_full</i>
        </div>
    </div>

    <!-- App Header with Logo -->
    <div class="app-header">
        <div class="header-logo">
            <img src="richland-logo.png" alt="Richland Golf Club" class="header-logo-img" onclick="showScreen('welcome-screen')">
        </div>
    </div>

    <!-- App Container -->
    <div class="app-container">
        <!-- Screen 1: Welcome Screen -->
        <div class="screen active" id="welcome-screen">
            <div class="background-image golf-course-bg"></div>
            <div class="screen-content">
                <div class="welcome-content">
                    <div class="app-logo">
                        <img src="richland-logo.png" alt="Richland Golf Club" class="club-logo">
                    </div>
                    <h1 class="app-title">Richland Golf Club</h1>

                    <button class="enhanced-fab" onclick="showScreen('phone-screen')">
                        <span>Get Started</span>
                        <i class="material-icons">arrow_forward</i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Screen 2: Phone Number Validation -->
        <div class="screen" id="phone-screen">
            <div class="background-image golf-course-bg dark-overlay"></div>
            <div class="screen-content phone-screen-content">
                <div class="phone-container">
                    <div class="phone-header">
                        <h2 class="phone-title">Enter Your Phone Number</h2>
                        <p class="phone-subtitle">We'll send a verification code to verify your account</p>
                    </div>

                    <div class="phone-input-section">
                        <div class="enhanced-phone-input">
                            <div class="country-selector">
                                <img src="https://flagcdn.com/w20/us.png" alt="US" class="country-flag">
                                <span class="country-code">+1</span>
                                <i class="material-icons">keyboard_arrow_down</i>
                            </div>
                            <div class="phone-input-wrapper">
                                <input type="tel" id="phone" class="phone-number-input" placeholder="(*************">
                            </div>
                        </div>
                        <p class="input-helper-text">
                            <i class="material-icons">info</i>
                            Standard messaging rates may apply
                        </p>
                    </div>

                    <div class="phone-actions">
                        <button class="enhanced-fab phone-submit-btn">
                            <span>Send Code</span>
                            <i class="material-icons">send</i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Screen 3: Verification Code -->
        <div class="screen" id="verification-screen">
            <div class="background-image golf-course-bg dark-overlay"></div>
            <div class="screen-content">
                <div class="app-bar">
                    <button class="icon-button" onclick="showScreen('phone-screen')">
                        <i class="material-icons">arrow_back</i>
                    </button>
                    <h2 class="app-bar-title">Verify Phone</h2>
                </div>

                <div class="verification-container">
                    <div class="verification-card">
                        <div class="verification-header">
                            <div class="verification-icon">
                                <i class="material-icons">sms</i>
                            </div>
                            <h2 class="screen-title">Enter Verification Code</h2>
                            <p class="verification-subtitle">We sent a 6-digit code to<br><strong>+****************</strong></p>
                        </div>

                        <div class="verification-code-input">
                            <input type="text" id="code-1" class="code-digit" maxlength="1" oninput="moveToNext(this, 'code-2')" onkeydown="moveToPrev(this, null, event)">
                            <input type="text" id="code-2" class="code-digit" maxlength="1" oninput="moveToNext(this, 'code-3')" onkeydown="moveToPrev(this, 'code-1', event)">
                            <input type="text" id="code-3" class="code-digit" maxlength="1" oninput="moveToNext(this, 'code-4')" onkeydown="moveToPrev(this, 'code-2', event)">
                            <input type="text" id="code-4" class="code-digit" maxlength="1" oninput="moveToNext(this, 'code-5')" onkeydown="moveToPrev(this, 'code-3', event)">
                            <input type="text" id="code-5" class="code-digit" maxlength="1" oninput="moveToNext(this, 'code-6')" onkeydown="moveToPrev(this, 'code-4', event)">
                            <input type="text" id="code-6" class="code-digit" maxlength="1" oninput="verifyCode()" onkeydown="moveToPrev(this, 'code-5', event)">
                        </div>

                        <div class="verification-timer">
                            <p class="timer-text">Resend code in <span id="countdown">30</span>s</p>
                            <button class="resend-button" id="resend-btn" onclick="resendCode()" disabled>
                                <i class="material-icons">refresh</i>
                                Resend Code
                            </button>
                        </div>

                        <button class="enhanced-fab verification-fab" id="verify-btn" onclick="showScreen('tee-time-screen')" disabled>
                            <span>Verify</span>
                            <i class="material-icons">check</i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Screen 4: Tee Time Selection -->
        <div class="screen" id="tee-time-screen">
            <div class="background-image golf-course-bg dark-overlay"></div>
            <div class="screen-content">
                <div class="app-bar">
                    <button class="icon-button" onclick="showScreen('phone-screen')">
                        <i class="material-icons">arrow_back</i>
                    </button>
                    <h2 class="app-bar-title">Select Tee Time</h2>
                </div>

                <div class="current-date-display">
                    <span class="current-date-text" id="current-date">5/20/2025 Friday</span>
                </div>

                <div class="tee-times-list">
                    <div class="premium-tee-card">
                        <div class="tee-card-header">
                            <div class="tee-time-display">
                                <span class="tee-time-main">8:00</span>
                                <span class="tee-time-period">AM</span>
                            </div>
                        </div>
                        <div class="tee-card-details">
                            <div class="tee-info-group">
                                <div class="player-selection">
                                    <span class="player-selection-label">
                                        <i class="material-icons">group</i>
                                        Players:
                                    </span>
                                    <div class="player-count-buttons">
                                        <button class="player-count-btn" data-count="1" onclick="selectPlayerCount(this, 1)">1</button>
                                        <button class="player-count-btn" data-count="2" onclick="selectPlayerCount(this, 2)">2</button>
                                        <button class="player-count-btn" data-count="3" onclick="selectPlayerCount(this, 3)">3</button>
                                        <button class="player-count-btn" data-count="4" onclick="selectPlayerCount(this, 4)">4</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="enhanced-select-button" onclick="showScreen('details-screen')" disabled>
                            <i class="material-icons">check_circle</i>
                            Select Time
                        </button>
                    </div>

                    <div class="premium-tee-card">
                        <div class="tee-card-header">
                            <div class="tee-time-display">
                                <span class="tee-time-main">8:10</span>
                                <span class="tee-time-period">AM</span>
                            </div>
                        </div>
                        <div class="tee-card-details">
                            <div class="tee-info-group">
                                <div class="player-selection">
                                    <span class="player-selection-label">
                                        <i class="material-icons">group</i>
                                        Players:
                                    </span>
                                    <div class="player-count-buttons">
                                        <button class="player-count-btn" data-count="1" onclick="selectPlayerCount(this, 1)">1</button>
                                        <button class="player-count-btn" data-count="2" onclick="selectPlayerCount(this, 2)">2</button>
                                        <button class="player-count-btn" data-count="3" onclick="selectPlayerCount(this, 3)">3</button>
                                        <button class="player-count-btn" data-count="4" onclick="selectPlayerCount(this, 4)">4</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="enhanced-select-button" onclick="showScreen('details-screen')" disabled>
                            <i class="material-icons">check_circle</i>
                            Select Time
                        </button>
                    </div>

                    <div class="premium-tee-card">
                        <div class="tee-card-header">
                            <div class="tee-time-display">
                                <span class="tee-time-main">8:20</span>
                                <span class="tee-time-period">AM</span>
                            </div>
                        </div>
                        <div class="tee-card-details">
                            <div class="tee-info-group">
                                <div class="player-selection">
                                    <span class="player-selection-label">
                                        <i class="material-icons">group</i>
                                        Players:
                                    </span>
                                    <div class="player-count-buttons">
                                        <button class="player-count-btn" data-count="1" onclick="selectPlayerCount(this, 1)">1</button>
                                        <button class="player-count-btn" data-count="2" onclick="selectPlayerCount(this, 2)">2</button>
                                        <button class="player-count-btn" data-count="3" onclick="selectPlayerCount(this, 3)">3</button>
                                        <button class="player-count-btn" data-count="4" onclick="selectPlayerCount(this, 4)">4</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="enhanced-select-button" onclick="showScreen('details-screen')" disabled>
                            <i class="material-icons">check_circle</i>
                            Select Time
                        </button>
                    </div>

                    <div class="premium-tee-card">
                        <div class="tee-card-header">
                            <div class="tee-time-display">
                                <span class="tee-time-main">8:30</span>
                                <span class="tee-time-period">AM</span>
                            </div>
                        </div>
                        <div class="tee-card-details">
                            <div class="tee-info-group">
                                <div class="player-selection">
                                    <span class="player-selection-label">
                                        <i class="material-icons">group</i>
                                        Players:
                                    </span>
                                    <div class="player-count-buttons">
                                        <button class="player-count-btn" data-count="1" onclick="selectPlayerCount(this, 1)">1</button>
                                        <button class="player-count-btn" data-count="2" onclick="selectPlayerCount(this, 2)">2</button>
                                        <button class="player-count-btn" data-count="3" onclick="selectPlayerCount(this, 3)">3</button>
                                        <button class="player-count-btn" data-count="4" onclick="selectPlayerCount(this, 4)">4</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="enhanced-select-button" onclick="showScreen('details-screen')" disabled>
                            <i class="material-icons">check_circle</i>
                            Select Time
                        </button>
                    </div>

                    <div class="premium-tee-card">
                        <div class="tee-card-header">
                            <div class="tee-time-display">
                                <span class="tee-time-main">8:40</span>
                                <span class="tee-time-period">AM</span>
                            </div>
                        </div>
                        <div class="tee-card-details">
                            <div class="tee-info-group">
                                <div class="player-selection">
                                    <span class="player-selection-label">
                                        <i class="material-icons">group</i>
                                        Players:
                                    </span>
                                    <div class="player-count-buttons">
                                        <button class="player-count-btn" data-count="1" onclick="selectPlayerCount(this, 1)">1</button>
                                        <button class="player-count-btn" data-count="2" onclick="selectPlayerCount(this, 2)">2</button>
                                        <button class="player-count-btn" data-count="3" onclick="selectPlayerCount(this, 3)">3</button>
                                        <button class="player-count-btn" data-count="4" onclick="selectPlayerCount(this, 4)">4</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="enhanced-select-button" onclick="showScreen('details-screen')" disabled>
                            <i class="material-icons">check_circle</i>
                            Select Time
                        </button>
                    </div>

                    <div class="premium-tee-card">
                        <div class="tee-card-header">
                            <div class="tee-time-display">
                                <span class="tee-time-main">8:50</span>
                                <span class="tee-time-period">AM</span>
                            </div>
                        </div>
                        <div class="tee-card-details">
                            <div class="tee-info-group">
                                <div class="player-selection">
                                    <span class="player-selection-label">
                                        <i class="material-icons">group</i>
                                        Players:
                                    </span>
                                    <div class="player-count-buttons">
                                        <button class="player-count-btn" data-count="1" onclick="selectPlayerCount(this, 1)">1</button>
                                        <button class="player-count-btn" data-count="2" onclick="selectPlayerCount(this, 2)">2</button>
                                        <button class="player-count-btn" data-count="3" onclick="selectPlayerCount(this, 3)">3</button>
                                        <button class="player-count-btn" data-count="4" onclick="selectPlayerCount(this, 4)">4</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="enhanced-select-button" onclick="showScreen('details-screen')" disabled>
                            <i class="material-icons">check_circle</i>
                            Select Time
                        </button>
                    </div>
                </div>

                <div class="help-banner">
                    <i class="material-icons">help_outline</i>
                    <span>Need help? Tap here</span>
                </div>
            </div>
        </div>

        <!-- Screen 5: Tee Time Details & Editing -->
        <div class="screen" id="details-screen">
            <div class="background-image golf-course-bg dark-overlay"></div>
            <div class="screen-content">
                <div class="app-bar">
                    <button class="icon-button" onclick="showScreen('tee-time-screen')">
                        <i class="material-icons">arrow_back</i>
                    </button>
                    <h2 class="app-bar-title">Booking Details</h2>
                </div>

                <!-- Modern Booking Summary Header -->
                <div class="modern-booking-header">
                    <div class="booking-info-card">
                        <div class="booking-info-row">
                            <div class="booking-info-item">
                                <div class="booking-icon">
                                    <i class="material-icons">schedule</i>
                                </div>
                                <div class="booking-text">
                                    <span class="booking-label">Tee Time</span>
                                    <span class="booking-value">8:00 AM - Tuesday, Oct 16</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Players Section -->
                <div class="modern-players-section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="material-icons">group</i>
                            Players (2)
                        </h3>
                    </div>

                    <!-- Modern Player Cards -->
                    <div class="modern-player-cards">
                        <!-- Player 1 Card -->
                        <div class="modern-player-card">
                            <div class="player-card-border"></div>
                            <div class="player-card-content">
                                <div class="player-main-info">
                                    <div class="player-avatar-modern">
                                        <i class="material-icons">person</i>
                                    </div>
                                    <div class="player-details-modern">
                                        <h4 class="player-name">Player 1</h4>
                                        <p class="player-role">Primary Booker</p>
                                    </div>
                                    <div class="player-price-modern">
                                        <span class="price-amount">$85.00</span>
                                        <span class="price-label">Adult</span>
                                    </div>
                                </div>
                                <div class="player-features">
                                    <div class="feature-tag">
                                        <i class="material-icons">golf_course</i>
                                        <span>18 Holes</span>
                                    </div>
                                    <div class="feature-tag">
                                        <i class="material-icons">directions_car</i>
                                        <span>Cart Included</span>
                                    </div>
                                    <div class="feature-tag">
                                        <i class="material-icons">golf_course</i>
                                        <span>Adult</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Player 2 Card -->
                        <div class="modern-player-card">
                            <div class="player-card-border"></div>
                            <div class="player-card-content">
                                <div class="player-main-info">
                                    <div class="player-avatar-modern">
                                        <i class="material-icons">person</i>
                                    </div>
                                    <div class="player-details-modern">
                                        <h4 class="player-name">Player 2</h4>
                                        <p class="player-role">Guest</p>
                                    </div>
                                    <div class="player-price-modern">
                                        <span class="price-amount">$85.00</span>
                                        <span class="price-label">Adult</span>
                                    </div>
                                </div>
                                <div class="player-features">
                                    <div class="feature-tag">
                                        <i class="material-icons">golf_course</i>
                                        <span>18 Holes</span>
                                    </div>
                                    <div class="feature-tag">
                                        <i class="material-icons">directions_car</i>
                                        <span>Cart Included</span>
                                    </div>
                                    <div class="feature-tag">
                                        <i class="material-icons">golf_course</i>
                                        <span>Adult</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Summary Card -->
                <div class="modern-summary-card">
                    <div class="summary-header">
                        <h3>Booking Summary</h3>
                        <div class="summary-badge">
                            <i class="material-icons">verified</i>
                            <span>Confirmed</span>
                        </div>
                    </div>
                    <div class="summary-breakdown">
                        <div class="summary-item">
                            <span>2 Players</span>
                            <span>$170.00</span>
                        </div>

                        <div class="summary-divider"></div>
                        <div class="summary-total">
                            <span>Total Amount</span>
                            <span>$200.00</span>
                        </div>
                    </div>
                </div>

                <!-- Modern Proceed Button -->
                <button class="modern-proceed-button" onclick="showScreen('payment-screen')">
                    <div class="button-content">
                        <i class="material-icons">payment</i>
                        <span>Proceed to Payment</span>
                        <i class="material-icons">arrow_forward</i>
                    </div>
                    <div class="button-shine"></div>
                </button>
            </div>
        </div>

        <!-- Screen 6: Payment Screen -->
        <div class="screen" id="payment-screen">
            <div class="background-image golf-course-bg dark-overlay"></div>
            <div class="screen-content">
                <div class="app-bar">
                    <button class="icon-button" onclick="showScreen('details-screen')">
                        <i class="material-icons">arrow_back</i>
                    </button>
                    <h2 class="app-bar-title">Payment</h2>
                </div>

                <!-- New Transaction Progress Bar -->
                <div class="transaction-progress">
                    <div class="progress-steps">
                        <div class="progress-step active" id="step1">
                            <div class="step-icon">
                                <i class="material-icons">credit_card</i>
                            </div>
                            <div class="step-label">Card Reading</div>
                        </div>
                        <div class="progress-step" id="step2">
                            <div class="step-icon">
                                <i class="material-icons">sync</i>
                            </div>
                            <div class="step-label">Processing</div>
                        </div>
                        <div class="progress-step" id="step3">
                            <div class="step-icon">
                                <i class="material-icons">security</i>
                            </div>
                            <div class="step-label">Authorization</div>
                        </div>
                        <div class="progress-step" id="step4">
                            <div class="step-icon">
                                <i class="material-icons">check_circle</i>
                            </div>
                            <div class="step-label">Complete</div>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>



                <button class="enhanced-fab payment-fab" onclick="startPaymentProcess()">
                    <i class="material-icons">payment</i>
                    <span>Pay $183.60</span>
                </button>

                <!-- Payment footer with simplified layout -->
                <div class="payment-footer">
                    <p class="secure-text">
                        <i class="material-icons">security</i>
                        Secure Payment
                    </p>
                </div>
            </div>
        </div>

        <!-- Screen 7: Success Screen -->
        <div class="screen" id="success-screen">
            <div class="background-image golf-course-bg dark-overlay"></div>
            <div class="screen-content">
                <div class="success-content">
                    <div class="enhanced-success-animation">
                        <div class="enhanced-checkmark-circle">
                            <i class="material-icons enhanced-checkmark">check</i>
                        </div>
                    </div>

                    <h2 class="success-title">Booking Confirmed!</h2>

                    <div class="enhanced-booking-summary">
                        <div class="enhanced-summary-item">
                            <div class="enhanced-summary-icon">
                                <i class="material-icons">schedule</i>
                            </div>
                            <span>8:00 AM - Tuesday, Oct 16</span>
                        </div>
                        <div class="enhanced-summary-item">
                            <div class="enhanced-summary-icon">
                                <i class="material-icons">golf_course</i>
                            </div>
                            <span>Pebble Beach Golf Links</span>
                        </div>
                        <div class="enhanced-summary-item">
                            <div class="enhanced-summary-icon">
                                <i class="material-icons">group</i>
                            </div>
                            <span>2 players</span>
                        </div>
                        <div class="enhanced-summary-item">
                            <div class="enhanced-summary-icon">
                                <i class="material-icons">payment</i>
                            </div>
                            <span>$183.60 total</span>
                        </div>
                    </div>

                    <button class="enhanced-fab" onclick="showScreen('welcome-screen')">
                        <i class="material-icons">home</i>
                        <span>Done</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Android Navigation Bar -->
    <div class="navigation-bar">
        <div class="nav-indicator"></div>
    </div>

    <!-- Edit Dialog -->
    <div class="edit-dialog-overlay" id="edit-dialog-overlay">
        <div class="edit-dialog">
            <div class="edit-dialog-header">
                <h3>Edit Booking Details</h3>
                <button class="close-dialog-btn" onclick="closeEditDialog()">
                    <i class="material-icons">close</i>
                </button>
            </div>

            <div class="edit-dialog-content">
                <!-- Player Name -->
                <div class="edit-section">
                    <label class="edit-label">Player Name</label>
                    <div class="enhanced-input-group">
                        <input type="text" id="edit-player-name" class="enhanced-input" placeholder=" " value="John Doe">
                        <label for="edit-player-name" class="enhanced-input-label">Full Name</label>
                    </div>
                </div>

                <!-- Holes Selection -->
                <div class="edit-section">
                    <label class="edit-label">Holes</label>
                    <div class="edit-options">
                        <button class="edit-option-btn selected" data-value="18" onclick="selectEditOption(this, 'holes')">18 Holes</button>
                        <button class="edit-option-btn" data-value="9" onclick="selectEditOption(this, 'holes')">9 Holes</button>
                    </div>
                </div>

                <!-- Cart/Walking Selection -->
                <div class="edit-section">
                    <label class="edit-label">Transportation</label>
                    <div class="edit-options">
                        <button class="edit-option-btn selected" data-value="walking" onclick="selectEditOption(this, 'transport')">Walking</button>
                        <button class="edit-option-btn" data-value="cart" onclick="selectEditOption(this, 'transport')">Cart</button>
                    </div>
                </div>

                <!-- Adult/Junior Selection -->
                <div class="edit-section">
                    <label class="edit-label">Player Type</label>
                    <div class="edit-options">
                        <button class="edit-option-btn selected" data-value="adult" onclick="selectEditOption(this, 'playertype')">Adult</button>
                        <button class="edit-option-btn" data-value="junior" onclick="selectEditOption(this, 'playertype')">Junior</button>
                    </div>
                </div>
            </div>

            <div class="edit-dialog-actions">
                <button class="button outlined" onclick="closeEditDialog()">Cancel</button>
                <button class="button contained" onclick="saveEditChanges()">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Snackbar -->
    <div class="snackbar" id="snackbar">
        <span>Receipt sent to email</span>
        <button class="snackbar-action">VIEW</button>
    </div>

    <script src="script.js"></script>
</body>
</html>